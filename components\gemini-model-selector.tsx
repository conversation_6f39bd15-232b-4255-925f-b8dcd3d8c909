"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Brain, CheckCircle, AlertTriangle, Loader2 } from "lucide-react"
import {
  getModelSelectOptions,
  getModelInfo,
  isValidModel,
  type ModelSelectOption,
} from "@/lib/gemini-models"
import { GeminiClient } from "@/lib/gemini-client"
import { toast } from "sonner"

interface GeminiModelSelectorProps {
  currentModel?: string
  apiKey?: string
  onModelChange: (modelId: string) => Promise<{ success: boolean; error?: string }>
  loading?: boolean
  disabled?: boolean
}

export function GeminiModelSelector({
  currentModel,
  api<PERSON><PERSON>,
  onModelChange,
  loading = false,
  disabled = false,
}: Readonly<GeminiModelSelectorProps>) {
  const [selectedModel, setSelectedModel] = useState(currentModel || "")
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)
  const [saving, setSaving] = useState(false)
  const [modelOptions, setModelOptions] = useState<ModelSelectOption[]>([])
  const [loadingModels, setLoadingModels] = useState(true)

  const currentModelInfo = getModelInfo(selectedModel)
  const hasApiKey = !!apiKey

  // Load model options on component mount and when API key changes
  useEffect(() => {
    const loadModelOptions = async () => {
      try {
        setLoadingModels(true)
        const options = await getModelSelectOptions(apiKey)
        setModelOptions(options)
      } catch (error) {
        console.error('Failed to load model options:', error)
        // Fallback to empty array, component will handle gracefully
        setModelOptions([])
      } finally {
        setLoadingModels(false)
      }
    }

    loadModelOptions()
  }, [apiKey])

  const testModel = async () => {
    if (!apiKey || !selectedModel) {
      toast.error("Test Requirements Missing", {
        description: "API key and model selection are required for testing.",
      })
      setTestResult({
        success: false,
        message: "API key and model selection are required for testing.",
      })
      return
    }

    setTesting(true)
    setTestResult(null)

    try {
      const client = new GeminiClient(apiKey, selectedModel)
      const result = await client.testConnection()

      if (result.success) {
        toast.success("Model Test Successful", {
          description: `${currentModelInfo?.name || selectedModel} is working correctly`,
        })
        setTestResult({
          success: true,
          message: `${currentModelInfo?.name || selectedModel} is working correctly.`,
        })
      } else {
        toast.error("Model Test Failed", {
          description: result.error || "Failed to test model connection",
        })
        setTestResult({
          success: false,
          message: result.error || "Failed to test model connection.",
        })
      }
    } catch (error: unknown) {
      toast.error("Model Test Error", {
        description: error instanceof Error ? error.message : "An unexpected error occurred during testing",
      })
      setTestResult({
        success: false,
        message: error.message || "An unexpected error occurred during testing.",
      })
    } finally {
      setTesting(false)
    }
  }

  const handleModelChange = async (modelId: string) => {
    const isValid = await isValidModel(modelId, apiKey)
    if (!isValid) {
      toast.error("Invalid Model", {
        description: "Selected model is not supported or has been deprecated.",
      })
      return
    }

    setSelectedModel(modelId)
    setTestResult(null)

    if (modelId === currentModel) {
      return // No change needed
    }

    setSaving(true)
    try {
      const result = await onModelChange(modelId)

      if (!result.success) {
        toast.error("Failed to Save", {
          description: result.error || "Failed to save model selection.",
        })
        // Revert selection on failure
        setSelectedModel(currentModel || "")
      } else {
        const modelInfo = getModelInfo(modelId)
        toast.success("Model Updated", {
          description: `Successfully switched to ${modelInfo?.name || modelId}`,
        })
      }
    } catch (error: unknown) {
      toast.error("Error", {
        description: error instanceof Error ? error.message : "Failed to save model selection.",
      })
      // Revert selection on failure
      setSelectedModel(currentModel || "")
    } finally {
      setSaving(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5" />
          Gemini AI Model
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* No Model Selected Warning */}
        {!selectedModel && (
          <Alert>
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>
              No AI model selected. Please select a model to enable AI features like recipe suggestions and nutrition analysis.
            </AlertDescription>
          </Alert>
        )}

        {/* Model Selection */}
        <div className="space-y-2">
          <Label htmlFor="gemini-model">AI Model</Label>
          <Select
            value={selectedModel}
            onValueChange={handleModelChange}
            disabled={loading || saving || disabled || loadingModels}
          >
            <SelectTrigger id="gemini-model">
              <SelectValue placeholder={loadingModels ? "Loading models..." : "Select a Gemini model"} />
            </SelectTrigger>
            <SelectContent>
              {loadingModels ? (
                <SelectItem value="loading" disabled>
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Loading models...
                  </div>
                </SelectItem>
              ) : modelOptions.length > 0 ? (
                modelOptions.map((option: ModelSelectOption) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    disabled={option.disabled}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span>{option.label}</span>
                      {option.badge && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {option.badge}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="no-models" disabled>
                  No models available
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Model Information */}
        {currentModelInfo && (
          <Alert>
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium">{currentModelInfo.name}</p>
                <p className="text-sm">{currentModelInfo.description}</p>
                <div className="flex flex-wrap gap-2 text-xs">
                  {currentModelInfo.capabilities.text && (
                    <Badge variant="outline">Text Generation</Badge>
                  )}
                  {currentModelInfo.capabilities.vision && (
                    <Badge variant="outline">Image Analysis</Badge>
                  )}
                  {currentModelInfo.capabilities.multimodal && (
                    <Badge variant="outline">Multimodal</Badge>
                  )}
                  {currentModelInfo.isRecommended && (
                    <Badge variant="default">Recommended</Badge>
                  )}
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Test Connection */}
        {hasApiKey && (
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              onClick={testModel}
              disabled={testing || loading || saving || !selectedModel}
              className="w-full bg-transparent"
            >
              {testing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Testing Model...
                </>
              ) : (
                <>
                  <Brain className="w-4 h-4 mr-2" />
                  Test Model Connection
                </>
              )}
            </Button>
          </div>
        )}

        {/* Test Results */}
        {testResult && (
          <Alert variant={testResult.success ? "default" : "destructive"}>
            {testResult.success ? (
              <CheckCircle className="w-4 h-4" />
            ) : (
              <AlertTriangle className="w-4 h-4" />
            )}
            <AlertDescription>{testResult.message}</AlertDescription>
          </Alert>
        )}

        {/* No API Key Warning */}
        {!hasApiKey && (
          <Alert>
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>
              Configure your Gemini API key above to test and use AI models.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {(loading || saving) && (
          <Alert>
            <Loader2 className="w-4 h-4 animate-spin" />
            <AlertDescription>
              {saving ? "Saving model selection..." : "Loading..."}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
