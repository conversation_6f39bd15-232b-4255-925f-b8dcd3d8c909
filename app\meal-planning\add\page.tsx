"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useRecipes } from "@/hooks/use-recipes"
import { useMealPlans } from "@/hooks/use-meal-plans"

export default function AddMealPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [formData, setFormData] = useState({
    date: searchParams.get("date") || new Date().toISOString().split("T")[0],
    meal: searchParams.get("meal") || "",
    recipeId: "",
  })

  const { recipes } = useRecipes()
  const { addMealPlan } = useMealPlans()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.date || !formData.meal || !formData.recipeId) {
      alert("Please fill in all fields")
      return
    }

    try {
      await addMealPlan({
        date: formData.date,
        meal_type: formData.meal as "breakfast" | "lunch" | "dinner",
        recipe_id: formData.recipeId,
      })

      router.push("/meal-planning")
    } catch (error) {
      console.error("Failed to add meal plan:", error)
      alert("Failed to add meal plan. Please try again.")
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-xs border-b sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center gap-3">
            <Link href="/meal-planning">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <h1 className="text-xl font-bold text-gray-900">Add Meal</h1>
          </div>
        </div>
      </div>

      <div className="p-4">
        <Card>
          <CardHeader>
            <CardTitle>Meal Details</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="meal">Meal Type</Label>
                <Select value={formData.meal} onValueChange={(value) => setFormData({ ...formData, meal: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select meal type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="breakfast">Breakfast</SelectItem>
                    <SelectItem value="lunch">Lunch</SelectItem>
                    <SelectItem value="dinner">Dinner</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="recipe">Recipe</Label>
                <Select
                  value={formData.recipeId}
                  onValueChange={(value) => setFormData({ ...formData, recipeId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select recipe" />
                  </SelectTrigger>
                  <SelectContent>
                    {recipes.map((recipe) => (
                      <SelectItem key={recipe.id} value={recipe.id}>
                        {recipe.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {recipes.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  <p>No recipes available. Add some recipes first.</p>
                  <Link href="/recipes/add">
                    <Button variant="outline" size="sm" className="mt-2 bg-transparent">
                      Add Recipe
                    </Button>
                  </Link>
                </div>
              )}

              <div className="flex gap-3 pt-4">
                <Link href="/meal-planning" className="flex-1">
                  <Button variant="outline" className="w-full bg-transparent">
                    Cancel
                  </Button>
                </Link>
                <Button type="submit" className="flex-1" disabled={recipes.length === 0}>
                  Add Meal
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
